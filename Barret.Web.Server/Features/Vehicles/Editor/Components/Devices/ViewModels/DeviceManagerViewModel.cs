using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features.Shared;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;
using System.Collections.Generic;
using System.Linq;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
{
    /// <summary>
    /// ViewModel for managing devices in a device group.
    /// </summary>
    public class DeviceManagerViewModel : ViewModelBase
    {
        private ObservableCollection<DeviceDto> _devices = new();
        private DeviceDto? _selectedDevice;
        private bool _isAddDeviceDialogVisible;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceManagerViewModel"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        public DeviceManagerViewModel(ILogger<DeviceManagerViewModel> logger) : base(logger)
        {
            // Initialize commands
            AddDeviceCommand = ReactiveCommand.Create(ShowAddDeviceDialog);
            RemoveDeviceCommand = ReactiveCommand.Create<DeviceDto>(RemoveDevice);
            EditDeviceCommand = ReactiveCommand.Create<DeviceDto>(EditDevice);
            SaveDeviceCommand = ReactiveCommand.Create(SaveDevice);
            CancelDeviceCommand = ReactiveCommand.Create(CancelDevice);
        }

        /// <summary>
        /// Gets the collection of devices.
        /// </summary>
        [Reactive]
        public ObservableCollection<DeviceDto> Devices
        {
            get => _devices;
            set => this.RaiseAndSetIfChanged(ref _devices, value);
        }

        /// <summary>
        /// Gets or sets the selected device for editing.
        /// </summary>
        [Reactive]
        public DeviceDto? SelectedDevice
        {
            get => _selectedDevice;
            set => this.RaiseAndSetIfChanged(ref _selectedDevice, value);
        }

        /// <summary>
        /// Gets or sets whether the add device dialog is visible.
        /// </summary>
        [Reactive]
        public bool IsAddDeviceDialogVisible
        {
            get => _isAddDeviceDialogVisible;
            set => this.RaiseAndSetIfChanged(ref _isAddDeviceDialogVisible, value);
        }

        /// <summary>
        /// Gets the command to add a new device.
        /// </summary>
        public ReactiveCommand<Unit, Unit> AddDeviceCommand { get; }

        /// <summary>
        /// Gets the command to remove a device.
        /// </summary>
        public ReactiveCommand<DeviceDto, Unit> RemoveDeviceCommand { get; }

        /// <summary>
        /// Gets the command to edit a device.
        /// </summary>
        public ReactiveCommand<DeviceDto, Unit> EditDeviceCommand { get; }

        /// <summary>
        /// Gets the command to save a device.
        /// </summary>
        public ReactiveCommand<Unit, Unit> SaveDeviceCommand { get; }

        /// <summary>
        /// Gets the command to cancel device editing.
        /// </summary>
        public ReactiveCommand<Unit, Unit> CancelDeviceCommand { get; }

        /// <summary>
        /// Event raised when a device is updated.
        /// </summary>
        public event Action<DeviceDto>? DeviceUpdated;

        /// <summary>
        /// Sets the devices collection.
        /// </summary>
        /// <param name="devices">The devices to set.</param>
        public void SetDevices(IEnumerable<DeviceDto> devices)
        {
            Devices.Clear();
            foreach (var device in devices)
            {
                Devices.Add(device);
            }
        }

        /// <summary>
        /// Shows the add device dialog.
        /// </summary>
        private void ShowAddDeviceDialog()
        {
            SelectedDevice = new DeviceDto
            {
                Name = "New Device",
                Connections = new List<DeviceConnectionDto>(),
                Alarms = new List<Barret.Shared.DTOs.Devices.Alarms.AlarmDto>()
            };
            IsAddDeviceDialogVisible = true;
        }

        /// <summary>
        /// Removes a device.
        /// </summary>
        /// <param name="device">The device to remove.</param>
        private void RemoveDevice(DeviceDto device)
        {
            if (Devices.Contains(device))
            {
                Devices.Remove(device);
                DeviceUpdated?.Invoke(device);
            }
        }

        /// <summary>
        /// Edits a device.
        /// </summary>
        /// <param name="device">The device to edit.</param>
        private void EditDevice(DeviceDto device)
        {
            SelectedDevice = device;
            IsAddDeviceDialogVisible = true;
        }

        /// <summary>
        /// Saves the current device.
        /// </summary>
        private void SaveDevice()
        {
            if (SelectedDevice != null)
            {
                // Add to devices if it's new
                if (!Devices.Contains(SelectedDevice))
                {
                    Devices.Add(SelectedDevice);
                }

                DeviceUpdated?.Invoke(SelectedDevice);
                
                // Close dialog
                IsAddDeviceDialogVisible = false;
                SelectedDevice = null;
            }
        }

        /// <summary>
        /// Cancels device editing.
        /// </summary>
        private void CancelDevice()
        {
            IsAddDeviceDialogVisible = false;
            SelectedDevice = null;
        }

        /// <summary>
        /// Gets the device status badge class.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The CSS class.</returns>
        public string GetDeviceStatusBadgeClass(DeviceDto device)
        {
            return device.IsActive() ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
        }

        /// <summary>
        /// Gets the device status text.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The status text.</returns>
        public string GetDeviceStatusText(DeviceDto device)
        {
            return device.IsActive() ? "Active" : "Inactive";
        }

        /// <summary>
        /// Gets the connection count for a device.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The connection count.</returns>
        public int GetConnectionCount(DeviceDto device)
        {
            return device.Connections?.Count ?? 0;
        }

        /// <summary>
        /// Gets the alarm count for a device.
        /// </summary>
        /// <param name="device">The device.</param>
        /// <returns>The alarm count.</returns>
        public int GetAlarmCount(DeviceDto device)
        {
            return device.Alarms?.Count ?? 0;
        }
    }
}
