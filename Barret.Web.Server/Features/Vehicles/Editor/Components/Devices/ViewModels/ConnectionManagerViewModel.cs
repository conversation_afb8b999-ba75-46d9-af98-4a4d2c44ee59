using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features.Shared;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;
using System.Collections.Generic;
using System.Linq;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
{
    /// <summary>
    /// ViewModel for managing device connections.
    /// </summary>
    public class ConnectionManagerViewModel : ViewModelBase
    {
        private DeviceDto _parentDevice = null!;
        private ObservableCollection<DeviceConnectionDto> _connections = new();
        private DeviceConnectionDto? _selectedConnection;
        private bool _isAddConnectionDialogVisible;

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionManagerViewModel"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        public ConnectionManagerViewModel(ILogger<ConnectionManagerViewModel> logger) : base(logger)
        {
            // Initialize commands
            AddConnectionCommand = ReactiveCommand.Create(ShowAddConnectionDialog);
            RemoveConnectionCommand = ReactiveCommand.Create<DeviceConnectionDto>(RemoveConnection);
            SaveConnectionCommand = ReactiveCommand.Create(SaveConnection);
            CancelConnectionCommand = ReactiveCommand.Create(CancelConnection);
        }

        /// <summary>
        /// Gets or sets the parent device.
        /// </summary>
        [Reactive]
        public DeviceDto ParentDevice
        {
            get => _parentDevice;
            set
            {
                this.RaiseAndSetIfChanged(ref _parentDevice, value);
                UpdateConnectionsFromDevice();
            }
        }

        /// <summary>
        /// Gets the collection of connections for the device.
        /// </summary>
        [Reactive]
        public ObservableCollection<DeviceConnectionDto> Connections
        {
            get => _connections;
            private set => this.RaiseAndSetIfChanged(ref _connections, value);
        }

        /// <summary>
        /// Gets or sets the selected connection for editing.
        /// </summary>
        [Reactive]
        public DeviceConnectionDto? SelectedConnection
        {
            get => _selectedConnection;
            set => this.RaiseAndSetIfChanged(ref _selectedConnection, value);
        }

        /// <summary>
        /// Gets or sets whether the add connection dialog is visible.
        /// </summary>
        [Reactive]
        public bool IsAddConnectionDialogVisible
        {
            get => _isAddConnectionDialogVisible;
            set => this.RaiseAndSetIfChanged(ref _isAddConnectionDialogVisible, value);
        }

        /// <summary>
        /// Gets the command to add a new connection.
        /// </summary>
        public ReactiveCommand<Unit, Unit> AddConnectionCommand { get; }

        /// <summary>
        /// Gets the command to remove a connection.
        /// </summary>
        public ReactiveCommand<DeviceConnectionDto, Unit> RemoveConnectionCommand { get; }

        /// <summary>
        /// Gets the command to save a connection.
        /// </summary>
        public ReactiveCommand<Unit, Unit> SaveConnectionCommand { get; }

        /// <summary>
        /// Gets the command to cancel connection editing.
        /// </summary>
        public ReactiveCommand<Unit, Unit> CancelConnectionCommand { get; }

        /// <summary>
        /// Event raised when the device is changed.
        /// </summary>
        public event Action<DeviceDto>? DeviceChanged;

        /// <summary>
        /// Shows the add connection dialog.
        /// </summary>
        private void ShowAddConnectionDialog()
        {
            SelectedConnection = new DeviceConnectionDto
            {
                ConnectedDeviceId = ParentDevice.Id,
                InterfaceDeviceId = Guid.Empty
            };
            IsAddConnectionDialogVisible = true;
        }

        /// <summary>
        /// Removes a connection from the device.
        /// </summary>
        /// <param name="connection">The connection to remove.</param>
        private void RemoveConnection(DeviceConnectionDto connection)
        {
            if (Connections.Contains(connection))
            {
                Connections.Remove(connection);
                UpdateDeviceFromConnections();
                DeviceChanged?.Invoke(ParentDevice);
            }
        }

        /// <summary>
        /// Saves the current connection.
        /// </summary>
        private void SaveConnection()
        {
            if (SelectedConnection != null)
            {
                // Add to connections if it's new
                if (!Connections.Contains(SelectedConnection))
                {
                    Connections.Add(SelectedConnection);
                }

                UpdateDeviceFromConnections();
                DeviceChanged?.Invoke(ParentDevice);
                
                // Close dialog
                IsAddConnectionDialogVisible = false;
                SelectedConnection = null;
            }
        }

        /// <summary>
        /// Cancels connection editing.
        /// </summary>
        private void CancelConnection()
        {
            IsAddConnectionDialogVisible = false;
            SelectedConnection = null;
        }

        /// <summary>
        /// Updates the connections collection from the device.
        /// </summary>
        private void UpdateConnectionsFromDevice()
        {
            if (ParentDevice?.Connections != null)
            {
                Connections.Clear();
                foreach (var connection in ParentDevice.Connections)
                {
                    Connections.Add(connection);
                }
            }
        }

        /// <summary>
        /// Updates the device connections from the collection.
        /// </summary>
        private void UpdateDeviceFromConnections()
        {
            if (ParentDevice != null)
            {
                ParentDevice.Connections = new List<DeviceConnectionDto>(Connections);
            }
        }

        /// <summary>
        /// Gets the connection type display text.
        /// </summary>
        /// <param name="connection">The connection.</param>
        /// <returns>The display text.</returns>
        public string GetConnectionTypeText(DeviceConnectionDto connection)
        {
            return connection.ConnectionType().ToString();
        }

        /// <summary>
        /// Gets the connection status badge class.
        /// </summary>
        /// <param name="connection">The connection.</param>
        /// <returns>The CSS class.</returns>
        public string GetConnectionStatusBadgeClass(DeviceConnectionDto connection)
        {
            return connection.IsActive() ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
        }

        /// <summary>
        /// Gets the connection status text.
        /// </summary>
        /// <param name="connection">The connection.</param>
        /// <returns>The status text.</returns>
        public string GetConnectionStatusText(DeviceConnectionDto connection)
        {
            return connection.IsActive() ? "Active" : "Inactive";
        }

    }
}
