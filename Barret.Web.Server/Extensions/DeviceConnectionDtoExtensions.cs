using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for DeviceConnectionDto
    /// </summary>
    public static class DeviceConnectionDtoExtensions
    {
        /// <summary>
        /// Gets the source device ID (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <returns>The connected device ID</returns>
        public static Guid SourceDeviceId(this DeviceConnectionDto connection)
        {
            return connection?.ConnectedDeviceId ?? Guid.Empty;
        }

        /// <summary>
        /// Gets the source device (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <returns>The source device DTO</returns>
        public static DeviceDto? SourceDevice(this DeviceConnectionDto connection, List<DeviceDto> allDevices)
        {
            if (connection == null || allDevices == null)
            {
                return null;
            }

            return allDevices.FirstOrDefault(d => d.Id == connection.ConnectedDeviceId);
        }

        /// <summary>
        /// Gets the target device (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <returns>The target device DTO</returns>
        public static DeviceDto? TargetDevice(this DeviceConnectionDto connection, List<DeviceDto> allDevices)
        {
            if (connection == null || allDevices == null)
            {
                return null;
            }

            return allDevices.FirstOrDefault(d => d.Id == connection.InterfaceDeviceId);
        }

        /// <summary>
        /// Gets the connection name (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <returns>A formatted connection name</returns>
        public static string ConnectionName(this DeviceConnectionDto connection)
        {
            if (connection == null)
            {
                return string.Empty;
            }

            return $"{connection.Type} Connection";
        }

        /// <summary>
        /// Gets the description for the connection (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <returns>A description string</returns>
        public static string Description(this DeviceConnectionDto connection)
        {
            if (connection == null)
            {
                return string.Empty;
            }

            return $"{connection.Type} connection with {connection.Direction} data flow";
        }

        /// <summary>
        /// Gets whether the connection is active (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <returns>True if the connection is considered active</returns>
        public static bool IsActive(this DeviceConnectionDto connection)
        {
            // For now, consider a connection active if it has valid device IDs and type
            return connection?.ConnectedDeviceId != Guid.Empty && 
                   connection?.InterfaceDeviceId != Guid.Empty &&
                   connection?.Type != ConnectionType.Undefined;
        }

        /// <summary>
        /// Gets the connection type (UI property)
        /// </summary>
        /// <param name="connection">The device connection DTO</param>
        /// <returns>The connection type</returns>
        public static ConnectionType ConnectionType(this DeviceConnectionDto connection)
        {
            return connection?.Type ?? ConnectionType.Undefined;
        }
    }
}
